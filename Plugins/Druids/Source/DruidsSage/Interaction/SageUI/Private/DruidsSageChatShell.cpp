#include "SDruidsSageChatShell.h"
#include "SDruidsSageChatView.h"

// UMG includes
#include "Blueprint/UserWidget.h"

//////////////////////////////////////////////////////////////////////////
// UDruidsSageChatShell - UMG implementation
//////////////////////////////////////////////////////////////////////////

UDruidsSageChatShell::UDruidsSageChatShell(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
}

void UDruidsSageChatShell::NativePreConstruct()
{
	Super::NativePreConstruct();
}

void UDruidsSageChatShell::NativeConstruct()
{
	Super::NativeConstruct();
}

TSharedRef<SWidget> UDruidsSageChatShell::RebuildWidget()
{
	TSharedRef<SWidget> Content = ConstructContent();

	if (!IsDesignTime())
	{
		// Initialize the chat view after construction
		InitializeChatView();
	}

	return Content;
}

void UDruidsSageChatShell::SynchronizeProperties()
{
	Super::SynchronizeProperties();

	// Properties are set during construction and initialization
	// No additional synchronization needed
}

void UDruidsSageChatShell::ReleaseSlateResources(bool bReleaseChildren)
{
	Super::ReleaseSlateResources(bReleaseChildren);

	OnMessageSending.Unbind();

	if (CurrentView.IsValid())
	{
		CurrentView.Reset();
	}

	if (ShellBox.IsValid())
	{
		ShellBox.Reset();
	}
}

void UDruidsSageChatShell::SetChatRequestHandler(TSharedPtr<IChatRequestHandler> InChatRequestHandler)
{
	ChatRequestHandler = InChatRequestHandler;

	// Update the chat view if it's already created
	if (CurrentView.IsValid())
	{
		CurrentView->SetChatRequestHandler(ChatRequestHandler);
	}
}

void UDruidsSageChatShell::SetExtensionDelegator(TSharedPtr<ISageExtensionDelegator> InExtensionDelegator)
{
	ExtensionDelegator = InExtensionDelegator;

	// Update the chat view if it's already created
	if (CurrentView.IsValid())
	{
		CurrentView->SetExtensionsDelegator(ExtensionDelegator);
	}
}

TSharedPtr<SDruidsSageChatView> UDruidsSageChatShell::GetCurrentView() const
{
	return CurrentView;
}

TSharedRef<SWidget> UDruidsSageChatShell::ConstructContent()
{
	return SAssignNew(ShellBox, SBox).HAlign(HAlign_Fill).VAlign(VAlign_Fill);
}

void UDruidsSageChatShell::InitializeChatView()
{
	if (!ShellBox.IsValid())
	{
		return;
	}

	ShellBox->SetContent(SAssignNew(CurrentView, SDruidsSageChatView));

	CurrentView->OnMessageSending.BindUObject(this, &UDruidsSageChatShell::OnChatViewMessageSending);

	if (ChatRequestHandler.IsValid())
	{
		CurrentView->SetChatRequestHandler(ChatRequestHandler);
	}

	if (ExtensionDelegator.IsValid())
	{
		CurrentView->SetExtensionsDelegator(ExtensionDelegator);
	}
}

void UDruidsSageChatShell::OnChatViewMessageSending()
{
	OnMessageSending.ExecuteIfBound();
}

