#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/MultiLineEditableTextBox.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"

#include "DruidsSageMultiLineTextInput.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnEnterPressed);

/**
 * Custom multi-line text input widget that handles Enter key to send messages
 * Wraps UMultiLineEditableTextBox with custom key handling behavior
 */
UCLASS(meta = (DisplayName = "Druids Sage Multi-Line Text Input"))
class SAGEUI_API UDruidsSageMultiLineTextInput : public UUserWidget
{
	GENERATED_BODY()

public:
	UDruidsSageMultiLineTextInput(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual TSharedRef<SWidget> RebuildWidget() override;
	virtual void SynchronizeProperties() override;
	virtual void ReleaseSlateResources(bool bReleaseChildren) override;
	// End of UUserWidget interface

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Text Input")
	FOnEnterPressed OnEnterPressed;

	// Public API
	UFUNCTION(BlueprintCallable, Category = "Text Input")
	void SetText(const FText& NewText);

	UFUNCTION(BlueprintCallable, Category = "Text Input")
	FText GetText() const;

	UFUNCTION(BlueprintCallable, Category = "Text Input")
	bool IsEmpty() const;

	UFUNCTION(BlueprintCallable, Category = "Text Input")
	void SetInputFocus();

protected:
	// Properties
	UPROPERTY(EditAnywhere, Category = "Text Input", meta = (DisplayName = "Text"))
	FText CurrentText;

	UPROPERTY(EditAnywhere, Category = "Text Input", meta = (DisplayName = "Hint Text"))
	FText HintText;

	UPROPERTY(EditAnywhere, Category = "Text Input", meta = (DisplayName = "Allow Context Menu"))
	bool bAllowContextMenu;

	UPROPERTY(EditAnywhere, Category = "Text Input", meta = (DisplayName = "Auto Wrap Text"))
	bool bAutoWrapText;

	// Internal callbacks
	void HandleTextChanged(const FText& Text);
	void HandleTextCommitted(const FText& Text, ETextCommit::Type CommitMethod);

private:
	// The underlying Slate widget
	TSharedPtr<class SCustomMultiLineEditableTextBox> SlateTextInput;
};

/**
 * Custom Slate multi-line text box that handles Enter key to send messages
 */
class SCustomMultiLineEditableTextBox : public SMultiLineEditableTextBox
{
public:
	SLATE_BEGIN_ARGS(SCustomMultiLineEditableTextBox) {}
		SLATE_EVENT(FSimpleDelegate, OnEnterPressed)
	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs);

	virtual FReply OnKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent) override;

private:
	FSimpleDelegate OnEnterPressed;
};
