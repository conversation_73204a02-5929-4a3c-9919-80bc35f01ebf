#pragma once

#include <CoreMinimal.h>

#include <Widgets/SCompoundWidget.h>
#include <Widgets/Layout/SBox.h>
#include "Blueprint/UserWidget.h"

class IChatRequestHandler;
class ISageExtensionDelegator;
class SDruidsSageChatView;

#include "SDruidsSageChatShell.generated.h"

UCLASS(meta = (DisplayName = "Druids Sage Chat Shell"))
class SAGEUI_API UDruidsSageChatShell : public UUserWidget
{
	GENERATED_BODY()

public:
	UDruidsSageChatShell(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual TSharedRef<SWidget> RebuildWidget() override;
	virtual void SynchronizeProperties() override;
	virtual void ReleaseSlateResources(bool bReleaseChildren) override;
	// End of UUserWidget interface

	void SetChatRequestHandler(TSharedPtr<IChatRequestHandler> InChatRequestHandler);
	void SetExtensionDelegator(TSharedPtr<ISageExtensionDelegator> InExtensionDelegator);

	// Get the current chat view
	TSharedPtr<SDruidsSageChatView> GetCurrentView() const;

	// Delegate for message sending
	DECLARE_DELEGATE(FOnMessageSendingUMG);
	FOnMessageSendingUMG OnMessageSending;

protected:
	// Properties that can be set via C++ (not exposed to Blueprint due to TSharedPtr)
	TSharedPtr<IChatRequestHandler> ChatRequestHandler;
	TSharedPtr<ISageExtensionDelegator> ExtensionDelegator;

	// BindWidget for Blueprint override - if set, this UMG widget will be used instead of the default Slate widget
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	class UUserWidget* CurrentView;

private:
	// Direct Slate widget management (no longer using SDruidsSageChatShell)
	TSharedPtr<SBox> ShellBox;
	TSharedPtr<SDruidsSageChatView> CurrentSlateView;

	// Internal methods
	TSharedRef<SWidget> ConstructContent();
	void InitializeChatView();
	void OnChatViewMessageSending();
};


