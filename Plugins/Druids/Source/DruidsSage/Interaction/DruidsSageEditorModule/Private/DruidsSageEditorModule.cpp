#include "DruidsSageEditorModule.h"

#include <ToolMenus.h>
#include <Widgets/Docking/SDockTab.h>

#include "Editor/Experimental/EditorInteractiveToolsFramework/Public/Behaviors/2DViewportBehaviorTargets.h"

#include "FBlueprintContextHandler.h"
#include "Framework/Application/SlateApplication.h"
#include "Misc/ConfigCacheIni.h"
#include "HAL/PlatformApplicationMisc.h"
#include "GenericPlatform/GenericPlatformApplicationMisc.h"
#include "Interfaces/IMainFrameModule.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

#include "PropertyEditorModule.h"
#include "FTabContextHandler.h"

#include "LogDruids.h"

#include "DruidsSageStyle.h"
#include "SDruidsSageChatView.h"
#include "DruidsSageHelper.h"

#include "Utils/DruidsSageClassDiscovery.h"

#include "SageExtensionDetails.h"
#include "SageExtension.h"
#include "ActiveSageExtensions.h"

#include "SDruidsSageChatShell.h"

#define LOCTEXT_NAMESPACE "FDruidsSageEditorModule"

void FDruidsSageEditorModule::StartupModule()
{
	// Initialize the style set
	FDruidsSageStyle::Initialize();
	FDruidsSageStyle::ReloadTextures();

	// Register this module as the editor interface
	FSageActiveObjectProvider::Set(this);
	
	BlueprintHandler = MakeShared<FBlueprintContextHandler>();
	BlueprintHandler->SetContextUpdateCallback([this]()
	{
		UpdateChatViewContext();
	});
	
	TabHandler = MakeShared<FTabContextHandler>();
	TabHandler->SetContextUpdateCallback([this]()
	{
		UpdateChatViewContext();
	});
	
	const FSimpleDelegate RegisterDelegate = FSimpleMulticastDelegate::FDelegate::CreateRaw(this, &FDruidsSageEditorModule::RegisterMenus);
	UToolMenus::RegisterStartupCallback(RegisterDelegate);

	RegisterMenus();

	FCoreDelegates::OnPostEngineInit.AddRaw(this, &FDruidsSageEditorModule::OnPostEngineInit);

	// Register detail customization
	FPropertyEditorModule& PropertyModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");
	PropertyModule.RegisterCustomClassLayout(
		USageExtension::StaticClass()->GetFName(),
		FOnGetDetailCustomizationInstance::CreateStatic(&FSageExtensionDetails::MakeInstance)
	);

	// Initialize the class discovery cache
	UDruidsSageClassDiscovery::Initialize();
}

void FDruidsSageEditorModule::ShutdownModule()
{
	// Shutdown the style set
	FDruidsSageStyle::Shutdown();

	FSageActiveObjectProvider::Set(nullptr);
	
	if (BlueprintHandler.IsValid())
	{
		BlueprintHandler.Reset();
	}

	if (TabHandler.IsValid())
	{
		TabHandler.Reset();
	}
	
	ActiveChatView = nullptr;
	LastCreatedShell = nullptr;

	if (GEditor)
	{
		GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetOpenedInEditor().RemoveAll(this);
		GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetClosedInEditor().RemoveAll(this);

		GEditor->OnEditorClose().RemoveAll(this);
	}

	// Clean up the window if it exists
	if (ChatWindowWeakPtr.IsValid())
	{
		ChatWindowWeakPtr.Pin()->RequestDestroyWindow();
		ChatWindowWeakPtr.Reset();
	}

	UToolMenus::UnRegisterStartupCallback(this);
	UToolMenus::UnregisterOwner(this);

	FCoreDelegates::OnPreExit.RemoveAll(this);

	if (FModuleManager::Get().IsModuleLoaded("PropertyEditor"))
	{
		FPropertyEditorModule& PropertyModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");
		PropertyModule.UnregisterCustomClassLayout(USageExtension::StaticClass()->GetFName());
	}
}

void FDruidsSageEditorModule::OnPostEngineInit()
{
	if (GEditor)
	{
		GEditor->OnEditorClose().AddRaw(this, &FDruidsSageEditorModule::OnEditorClose);
	}
}

TSharedRef<SWindow> FDruidsSageEditorModule::CreateFloatingChatWindow()
{
	// Load saved options
	FDruidsSageCommonOptions Options = UDruidsSageHelper::LoadCommonOptionsFromIni();

	// Ensure minimum window size
	float WindowWidth = FMath::Max(Options.WindowWidth, Options.MinWindowWidth);
	float WindowHeight = FMath::Max(Options.WindowHeight, Options.MinWindowHeight);
	FVector2D InitialSize(WindowWidth, WindowHeight);

	// Validate and adjust window position
	FVector2D WindowPosition(Options.WindowPositionX, Options.WindowPositionY);

	// Check if we have a saved position (using sentinel values to indicate "not set")
	// Both X and Y must be -1.0f to indicate no saved position
	bool bHasSavedPosition = !(Options.WindowPositionX == -1.0f && Options.WindowPositionY == -1.0f);

	if (bHasSavedPosition)
	{
		// Check if the saved position is still on any available monitor
		if (!IsPositionOnValidMonitor(WindowPosition, InitialSize))
		{
			UE_LOG(LogDruidsSage, Warning, TEXT("Saved window position (%.1f, %.1f) is no longer on any available monitor. Using default position."),
				WindowPosition.X, WindowPosition.Y);
			WindowPosition = FVector2D(-1.0f, -1.0f); // Use default positioning
		}
		else
		{
			// Validate the position is fully on screen (adjust if partially off-screen)
			FVector2D OriginalPosition = WindowPosition;
			WindowPosition = ValidateWindowPosition(WindowPosition, InitialSize);

			if (OriginalPosition != WindowPosition)
			{
				UE_LOG(LogDruidsSage, Log, TEXT("Window position adjusted from (%.1f, %.1f) to (%.1f, %.1f) to ensure it's fully visible"),
					OriginalPosition.X, OriginalPosition.Y, WindowPosition.X, WindowPosition.Y);
			}
			else
			{
				UE_LOG(LogDruidsSage, Log, TEXT("Window position (%.1f, %.1f) is valid, no adjustment needed"), WindowPosition.X, WindowPosition.Y);
			}
		}
	}
	else
	{
		UE_LOG(LogDruidsSage, Log, TEXT("No saved window position found, using default positioning"));
	}

	// Create the window
	TSharedRef<SWindow> NewWindow = SNew(SWindow)
		.Title(FText::FromString(TEXT("Druids Sage Chat")))
		.ClientSize(InitialSize) // Use validated size
		.SupportsMaximize(true)         // Allow maximization
		.SupportsMinimize(false)          // Do not allow minimization
		.SizingRule(ESizingRule::UserSized) // Allow resizing
		.IsInitiallyMaximized(false)
		.HasCloseButton(true);

	// Set the content
	if (OnCreateChatShell.IsBound())
	{
		LastCreatedShell = OnCreateChatShell.Execute();
	}

	if (LastCreatedShell)
	{
		// Get the current Slate view from the UMG widget
		ActiveChatView = LastCreatedShell->GetCurrentSlateView();

		// Bind the message sending delegate using a lambda
		LastCreatedShell->OnMessageSending.BindRaw(this, &FDruidsSageEditorModule::UpdateActiveExtensions);

		// Use TakeWidget() to get the Slate widget from the UMG widget
		TSharedRef<SWidget> SlateWidget = LastCreatedShell->TakeWidget();
		NewWindow->SetContent(SlateWidget);
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Error, TEXT("FDruidsSageEditorModule::CreateFloatingChatWindow() - ChatShell was not created"));
	}

	UpdateChatViewContext();

	NewWindow->SetOnWindowClosed(FOnWindowClosed::CreateRaw(this, &FDruidsSageEditorModule::OnWindowClosed));

	// Get the main editor window as parent to ensure proper window hierarchy
	IMainFrameModule& MainFrameModule = FModuleManager::LoadModuleChecked<IMainFrameModule>(TEXT("MainFrame"));
	TSharedPtr<SWindow> ParentWindow = MainFrameModule.GetParentWindow();

	if (ParentWindow.IsValid())
	{
		// Add as child window to ensure it minimizes with the editor
		FSlateApplication::Get().AddWindowAsNativeChild(NewWindow, ParentWindow.ToSharedRef());
	}
	else
	{
		// Fallback to regular window if parent not available
		FSlateApplication::Get().AddWindow(NewWindow);
	}

	// Set window position after adding to application (if we have a valid saved position)
	if (WindowPosition.X != -1.0f && WindowPosition.Y != -1.0f)
	{
		UE_LOG(LogDruidsSage, Log, TEXT("Moving window to saved position: (%.1f, %.1f)"), WindowPosition.X, WindowPosition.Y);
		NewWindow->MoveWindowTo(WindowPosition);
	}
	else
	{
		UE_LOG(LogDruidsSage, Log, TEXT("Using default window positioning"));
	}

	// Set focus to the input text box after window is fully shown
	if (ActiveChatView.IsValid() && GWorld)
	{
		FTimerHandle TimerHandle;
		GWorld->GetTimerManager().SetTimer(TimerHandle, [this]()
		{
			if (ActiveChatView.IsValid())
			{
				ActiveChatView.Pin()->SetInputFocus();
			}
		}, 0.1f, false); // Small delay to ensure window is fully shown
	}

	return NewWindow;
}

void FDruidsSageEditorModule::SaveWindowSize(const FVector2D& NewSize)
{
	// Save the window size to config
	if (GConfig)
	{
		GConfig->SetVector2D(
			TEXT("DruidsSage.ChatWindow"),
			TEXT("Size"),
			NewSize,
			GEditorPerProjectIni
		);
		GConfig->Flush(false, GEditorPerProjectIni);
	}
}

void FDruidsSageEditorModule::SaveCurrentChatWindowSize() const
{
	if (ChatWindowWeakPtr.IsValid())
	{
		TSharedPtr<SWindow> Window = ChatWindowWeakPtr.Pin();
		if (Window.IsValid())
		{
			SaveWindowSize(Window->GetClientSizeInScreen());
		}
	}
}

void FDruidsSageEditorModule::OnWindowClosed(const TSharedRef<SWindow>& Window)
{
	SaveCurrentChatWindowState();

	if (ChatWindowWeakPtr.IsValid() && ChatWindowWeakPtr.Pin() == Window)
	{
		// Clear the weak pointer and reset timing
		ChatWindowWeakPtr.Reset();
	}

	LastCreatedShell = nullptr;
}

void FDruidsSageEditorModule::UpdateChatViewContext() const
{
	if (ActiveChatView.IsValid())
	{
		// Get Tab Context from TabHandler
		if (TabHandler.IsValid())
		{
			FString TabDisplayContext;
			FString TabFullContext;
			TWeakObjectPtr ActiveObject = TabHandler->RefreshTabContext(TabFullContext, TabDisplayContext);
			
			ActiveChatView.Pin()->SetTabContext(TabFullContext, TabDisplayContext);
			ActiveChatView.Pin()->SetActiveObject(ActiveObject);
		}
		
		// Get BP Context from BlueprintHandler
		if (BlueprintHandler.IsValid() && BlueprintHandler->IsBlueprintFocused())
		{
			FString BPFullContext;
			FString BPDisplayContext;
			BlueprintHandler->GetBlueprintContext(BPFullContext, BPDisplayContext);
			ActiveChatView.Pin()->SetBPContext(BPFullContext, BPDisplayContext);
		}
		else
		{
			ActiveChatView.Pin()->SetBPContext(TEXT(""), TEXT(""));
		}
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Display, TEXT("UpdateChatViewContext called but ActiveChatView is not valid."));
	}
}

void FDruidsSageEditorModule::OnEditorClose()
{
	if (TabHandler.IsValid())
	{
		TabHandler->Cleanup();
	}
}

void FDruidsSageEditorModule::OnEnginePreExit() const
{
	SaveCurrentChatWindowState();
}

void FDruidsSageEditorModule::ToggleChatWindow()
{
	// Check if the window already exists and is valid
	if (ChatWindowWeakPtr.IsValid() && ChatWindowWeakPtr.Pin()->IsVisible())
	{
		TSharedPtr<SWindow> ChatWindow = ChatWindowWeakPtr.Pin();

		LastCreatedShell = nullptr;
		ChatWindow->RequestDestroyWindow();
		ChatWindowWeakPtr.Reset();
		return;
	}

	// Window doesn't exist or isn't visible, so create/open it
	TSharedRef<SWindow> NewWindow = CreateFloatingChatWindow();
	ChatWindowWeakPtr = NewWindow; // Store the weak pointer
}

void FDruidsSageEditorModule::RegisterMenus()
{
	FToolMenuOwnerScoped OwnerScoped(this);

	// Add a menu entry under the "Tools" menu
	UToolMenu* MenuTool = UToolMenus::Get()->ExtendMenu("LevelEditor.MainMenu.Tools");
	FToolMenuSection& Section = MenuTool->AddSection("DruidsSageSection", LOCTEXT("DruidsSageSection", "DruidsSage"));
	Section.AddMenuEntry(
		"ToggleDruidsSageChat",
		LOCTEXT("DruidsSageChatLabel", "Druids Sage Chat"),
		LOCTEXT("DruidsSageChatTooltip", "Open/Close DruidsSage Chat Window (brings to front if behind other windows)"),
		FSlateIcon(FDruidsSageStyle::GetStyleSetName(), "DruidsSage.ChatIcon"),
		FUIAction(
			FExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::ToggleChatWindow)
		)
	);

	// Add toolbar buttons to multiple editor toolbars
	RegisterToolbarButtons();
}

void FDruidsSageEditorModule::RegisterToolbarButtons()
{
	// List of toolbar menus to extend
	TArray<FString> ToolbarMenuNames = {
		// Level Editor
		"LevelEditor.LevelEditorToolBar.PlayToolBar",           // Level Editor Play toolbar

		// Blueprint-related Editors
		"AssetEditor.BlueprintEditor.ToolBar",                 // Blueprint Editor toolbar
		"AssetEditor.BlueprintStructEditor.ToolBar",           // Blueprint Struct Editor toolbar
		"AssetEditor.BlueprintEnumEditor.ToolBar",             // Blueprint Enum Editor toolbar
		"AssetEditor.BlueprintInterfaceEditor.ToolBar",        // Blueprint Interface Editor toolbar
		"AssetEditor.WidgetBlueprintEditor.ToolBar",           // Widget Blueprint Editor toolbar

		// Material and Texture Editors
		"AssetEditor.MaterialEditor.ToolBar",                  // Material Editor toolbar
		"AssetEditor.MaterialInstanceEditor.ToolBar",          // Material Instance Editor toolbar
		"AssetEditor.TextureEditor.ToolBar",                   // Texture Editor toolbar
		"AssetEditor.RenderTargetEditor.ToolBar",              // Render Target Editor toolbar

		// Mesh Editors
		"AssetEditor.StaticMeshEditor.ToolBar",                // Static Mesh Editor toolbar
		"AssetEditor.SkeletalMeshEditor.ToolBar",              // Skeletal Mesh Editor toolbar
		"AssetEditor.PhysicsAssetEditor.ToolBar",              // Physics Asset Editor toolbar

		// Animation Editors
		"AssetEditor.AnimationEditor.ToolBar",                 // Animation Editor toolbar
		"AssetEditor.AnimationBlueprintEditor.ToolBar",        // Animation Blueprint Editor toolbar
		"AssetEditor.ControlRigEditor.ToolBar",                // Control Rig Editor toolbar

		// Audio Editors
		"AssetEditor.SoundCueEditor.ToolBar",                  // Sound Cue Editor toolbar
		"AssetEditor.SoundWaveEditor.ToolBar",                 // Sound Wave Editor toolbar

		// Particle and VFX Editors
		"AssetEditor.ParticleSystemEditor.ToolBar",            // Particle System Editor toolbar
		"AssetEditor.NiagaraEditor.ToolBar",                   // Niagara Editor toolbar

		// AI Editors
		"AssetEditor.BehaviorTreeEditor.ToolBar",              // Behavior Tree Editor toolbar
		"AssetEditor.BlackboardEditor.ToolBar",                // Blackboard Editor toolbar

		// Data Editors
		"AssetEditor.DataTableEditor.ToolBar",                 // Data Table Editor toolbar
		"AssetEditor.CurveTableEditor.ToolBar",                // Curve Table Editor toolbar
		"AssetEditor.CurveEditor.ToolBar",                     // Curve Editor toolbar

		// Sequencer and Media
		"AssetEditor.LevelSequenceEditor.ToolBar",             // Level Sequence Editor toolbar
		"AssetEditor.MediaPlayerEditor.ToolBar",               // Media Player Editor toolbar

		// Font and UI
		"AssetEditor.FontEditor.ToolBar",                      // Font Editor toolbar
		"AssetEditor.SlateWidgetStyleEditor.ToolBar",          // Slate Widget Style Editor toolbar

		// Landscape and Foliage
		"AssetEditor.LandscapeEditor.ToolBar",                 // Landscape Editor toolbar
		"AssetEditor.FoliageTypeEditor.ToolBar"                // Foliage Type Editor toolbar
	};

	// Add toolbar button to each specified toolbar
	for (const FString& MenuName : ToolbarMenuNames)
	{
		AddToolbarButtonToMenu(MenuName);
	}
}

void FDruidsSageEditorModule::AddToolbarButtonToMenu(const FString& MenuName)
{
	UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu(*MenuName);
	if (ToolbarMenu)
	{
		FToolMenuSection& ToolbarSection = ToolbarMenu->FindOrAddSection("DruidsSage");
		FToolMenuEntry& Entry = ToolbarSection.AddEntry(FToolMenuEntry::InitToolBarButton(
			"ToggleDruidsSageChat",
			FUIAction(
				FExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::ToggleChatWindow)
			),
			LOCTEXT("DruidsSageChatLabel", "Druids Sage"),
			LOCTEXT("DruidsSageChatTooltip", "Open/Close DruidsSage Chat Window (brings to front if behind other windows)"),
			FSlateIcon(FDruidsSageStyle::GetStyleSetName(), "DruidsSage.ChatIcon")
		));

		UE_LOG(LogDruidsSage, Log, TEXT("Added DruidsSage toolbar button to: %s"), *MenuName);
	}
	else
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Failed to extend toolbar menu: %s"), *MenuName);
	}
}

TWeakObjectPtr<> FDruidsSageEditorModule::GetActiveObject() const
{
	return TabHandler.IsValid() ? TabHandler->GetActiveObject() : TWeakObjectPtr();
}

void FDruidsSageEditorModule::SaveCurrentChatWindowState() const
{
	if (ChatWindowWeakPtr.IsValid())
	{
		TSharedPtr<SWindow> Window = ChatWindowWeakPtr.Pin();
		if (Window.IsValid())
		{
			// Get current window state
			FVector2D WindowSize = Window->GetClientSizeInScreen();
			FVector2D WindowPosition = Window->GetPositionInScreen();
			FString MonitorName = GetCurrentMonitorName(WindowPosition);

			// Create options structure with current values
			FDruidsSageCommonOptions Options = UDruidsSageHelper::LoadCommonOptionsFromIni();
			Options.WindowWidth = WindowSize.X;
			Options.WindowHeight = WindowSize.Y;
			Options.WindowPositionX = WindowPosition.X;
			Options.WindowPositionY = WindowPosition.Y;

			// Save to INI file
			UDruidsSageHelper::SaveWindowOptionsToIni(Options);
		}
	}
}

FVector2D FDruidsSageEditorModule::ValidateWindowPosition(const FVector2D& Position, const FVector2D& Size) const
{
	// Get the work area of all monitors
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	FVector2D ValidatedPosition = Position;

	// Check if the window would be completely off-screen
	bool bFoundValidMonitor = false;
	for (const FMonitorInfo& Monitor : DisplayMetrics.MonitorInfo)
	{
		const FPlatformRect& MonitorRect = Monitor.WorkArea;

		// Check if any part of the window would be visible on this monitor
		// Convert to simple bounds checking
		float WindowLeft = Position.X;
		float WindowTop = Position.Y;
		float WindowRight = Position.X + Size.X;
		float WindowBottom = Position.Y + Size.Y;

		// Check if rectangles intersect
		bool bIntersects = !(WindowRight < MonitorRect.Left ||
		                    WindowLeft > MonitorRect.Right ||
		                    WindowBottom < MonitorRect.Top ||
		                    WindowTop > MonitorRect.Bottom);

		if (bIntersects)
		{
			bFoundValidMonitor = true;

			// Ensure the window is not positioned off the edges of this monitor
			ValidatedPosition.X = FMath::Clamp(Position.X, (float)MonitorRect.Left, (float)MonitorRect.Right - Size.X);
			ValidatedPosition.Y = FMath::Clamp(Position.Y, (float)MonitorRect.Top, (float)MonitorRect.Bottom - Size.Y);
			break;
		}
	}

	// If no valid monitor found, use primary monitor
	if (!bFoundValidMonitor && DisplayMetrics.MonitorInfo.Num() > 0)
	{
		const FPlatformRect& PrimaryMonitor = DisplayMetrics.PrimaryDisplayWorkAreaRect;
		ValidatedPosition.X = (float)PrimaryMonitor.Left + 100; // Small offset from edge
		ValidatedPosition.Y = (float)PrimaryMonitor.Top + 100;
	}

	return ValidatedPosition;
}

FString FDruidsSageEditorModule::GetCurrentMonitorName(const FVector2D& WindowPosition) const
{
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	UE_LOG(LogDruidsSage, Log, TEXT("Getting monitor name for window position: (%.1f, %.1f)"), WindowPosition.X, WindowPosition.Y);

	// Find which monitor contains the window position
	for (int32 i = 0; i < DisplayMetrics.MonitorInfo.Num(); ++i)
	{
		const FMonitorInfo& Monitor = DisplayMetrics.MonitorInfo[i];
		const FPlatformRect& MonitorRect = Monitor.WorkArea;

		UE_LOG(LogDruidsSage, Log, TEXT("Checking monitor [%d] '%s': Left=%d, Top=%d, Right=%d, Bottom=%d"),
			i, *Monitor.Name, MonitorRect.Left, MonitorRect.Top, MonitorRect.Right, MonitorRect.Bottom);

		// Check if the point is within this monitor's bounds
		if (WindowPosition.X >= MonitorRect.Left && WindowPosition.X <= MonitorRect.Right &&
		    WindowPosition.Y >= MonitorRect.Top && WindowPosition.Y <= MonitorRect.Bottom)
		{
			UE_LOG(LogDruidsSage, Log, TEXT("Window is on monitor '%s'"), *Monitor.Name);
			return Monitor.Name;
		}
	}

	// If not found on any monitor, return primary monitor identifier
	if (DisplayMetrics.MonitorInfo.Num() > 0)
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Window position not found on any monitor, using first monitor '%s'"), *DisplayMetrics.MonitorInfo[0].Name);
		return DisplayMetrics.MonitorInfo[0].Name; // Use first monitor as fallback
	}

	UE_LOG(LogDruidsSage, Error, TEXT("No monitors found!"));
	return TEXT("Unknown");
}

bool FDruidsSageEditorModule::IsPositionOnValidMonitor(const FVector2D& Position, const FVector2D& Size) const
{
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	UE_LOG(LogDruidsSage, Log, TEXT("Checking if position (%.1f, %.1f) with size (%.1f, %.1f) is on any monitor"),
		Position.X, Position.Y, Size.X, Size.Y);

	// Check if the window would be visible on any monitor
	for (int32 i = 0; i < DisplayMetrics.MonitorInfo.Num(); ++i)
	{
		const FMonitorInfo& Monitor = DisplayMetrics.MonitorInfo[i];
		const FPlatformRect& MonitorRect = Monitor.WorkArea;

		// Calculate window bounds
		float WindowLeft = Position.X;
		float WindowTop = Position.Y;
		float WindowRight = Position.X + Size.X;
		float WindowBottom = Position.Y + Size.Y;

		// Check if rectangles intersect
		bool bIntersects = !(WindowRight < MonitorRect.Left ||
		                    WindowLeft > MonitorRect.Right ||
		                    WindowBottom < MonitorRect.Top ||
		                    WindowTop > MonitorRect.Bottom);

		UE_LOG(LogDruidsSage, Log, TEXT("  Monitor [%d] '%s': Bounds(L=%d, T=%d, R=%d, B=%d), Window(L=%.1f, T=%.1f, R=%.1f, B=%.1f), Intersects=%s"),
			i, *Monitor.Name, MonitorRect.Left, MonitorRect.Top, MonitorRect.Right, MonitorRect.Bottom,
			WindowLeft, WindowTop, WindowRight, WindowBottom, bIntersects ? TEXT("Yes") : TEXT("No"));

		// If any part of the window intersects with this monitor, it's valid
		if (bIntersects)
		{
			UE_LOG(LogDruidsSage, Log, TEXT("Position is valid - found on monitor '%s'"), *Monitor.Name);
			return true;
		}
	}

	UE_LOG(LogDruidsSage, Warning, TEXT("Position is NOT valid - not found on any monitor"));
	return false;
}

void FDruidsSageEditorModule::UpdateActiveExtensions()
{
	TWeakObjectPtr<UObject> ActiveObject = GetActiveObject();
	TArray<TWeakObjectPtr<USageExtension>> ActiveExtensions = FActiveSageExtensions::Get().
		GetActiveExtensionsForContext(ActiveObject);
	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionsDefinitions = FActiveSageExtensions::Get().
		GetDefinitionsFromExtensions(ActiveExtensions);
	
	// Update the chat view with new extensions
	if (ActiveChatView.IsValid())
	{
		ActiveChatView.Pin()->SetActiveExtensionDefinitions(ActiveExtensionsDefinitions);
	}
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FDruidsSageEditorModule, DruidsSageEditorModule)
