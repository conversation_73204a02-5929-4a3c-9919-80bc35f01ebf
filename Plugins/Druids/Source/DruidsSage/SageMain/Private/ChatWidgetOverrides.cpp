#include "ChatWidgetOverrides.h"
#include "DruidsSageChatShell.h"

UChatWidgetOverrides::UChatWidgetOverrides()
{
	// Initialize with nullptr - will use base UDruidsSageChatShell class by default
	DefaultChatShellClass = nullptr;
}

TSubclassOf<UDruidsSageChatShell> UChatWidgetOverrides::GetChatShellWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultChatShellClass;
}
